[{"D:\\Project\\salon-management-system\\src\\index.js": "1", "D:\\Project\\salon-management-system\\src\\reportWebVitals.js": "2", "D:\\Project\\salon-management-system\\src\\App.js": "3", "D:\\Project\\salon-management-system\\src\\components\\Dashboard.js": "4", "D:\\Project\\salon-management-system\\src\\components\\Navbar.js": "5", "D:\\Project\\salon-management-system\\src\\components\\Appointments.js": "6", "D:\\Project\\salon-management-system\\src\\components\\Staff.js": "7", "D:\\Project\\salon-management-system\\src\\components\\Reports.js": "8", "D:\\Project\\salon-management-system\\src\\components\\Customers.js": "9", "D:\\Project\\salon-management-system\\src\\components\\Services.js": "10", "D:\\Project\\salon-management-system\\src\\components\\ProtectedRoute.js": "11", "D:\\Project\\salon-management-system\\src\\components\\Register.js": "12", "D:\\Project\\salon-management-system\\src\\components\\UserProfile.js": "13", "D:\\Project\\salon-management-system\\src\\components\\Login.js": "14", "D:\\Project\\salon-management-system\\src\\contexts\\AuthContext.js": "15"}, {"size": 535, "mtime": 1752673505339, "results": "16", "hashOfConfig": "17"}, {"size": 362, "mtime": 1752673505478, "results": "18", "hashOfConfig": "17"}, {"size": 3540, "mtime": 1752676443684, "results": "19", "hashOfConfig": "17"}, {"size": 14201, "mtime": 1752676565731, "results": "20", "hashOfConfig": "17"}, {"size": 7082, "mtime": 1752676396817, "results": "21", "hashOfConfig": "17"}, {"size": 12423, "mtime": 1752674639188, "results": "22", "hashOfConfig": "17"}, {"size": 15856, "mtime": 1752674665958, "results": "23", "hashOfConfig": "17"}, {"size": 15811, "mtime": 1752674110853, "results": "24", "hashOfConfig": "17"}, {"size": 12999, "mtime": 1752674653563, "results": "25", "hashOfConfig": "17"}, {"size": 13433, "mtime": 1752673981545, "results": "26", "hashOfConfig": "17"}, {"size": 4536, "mtime": 1752676872090, "results": "27", "hashOfConfig": "17"}, {"size": 9380, "mtime": 1752676890351, "results": "28", "hashOfConfig": "17"}, {"size": 9119, "mtime": 1752676328242, "results": "29", "hashOfConfig": "17"}, {"size": 6463, "mtime": 1752676861107, "results": "30", "hashOfConfig": "17"}, {"size": 4995, "mtime": 1752676181967, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "wsws0p", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Project\\salon-management-system\\src\\index.js", [], [], "D:\\Project\\salon-management-system\\src\\reportWebVitals.js", [], [], "D:\\Project\\salon-management-system\\src\\App.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Dashboard.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Navbar.js", ["77", "78", "79"], [], "D:\\Project\\salon-management-system\\src\\components\\Appointments.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Staff.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Reports.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Customers.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Services.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\ProtectedRoute.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Register.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\UserProfile.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Login.js", [], [], "D:\\Project\\salon-management-system\\src\\contexts\\AuthContext.js", [], [], {"ruleId": "80", "severity": 1, "message": "81", "line": 35, "column": 25, "nodeType": "82", "messageId": "83", "endLine": 35, "endColumn": 32}, {"ruleId": "80", "severity": 1, "message": "84", "line": 35, "column": 34, "nodeType": "82", "messageId": "83", "endLine": 35, "endColumn": 41}, {"ruleId": "80", "severity": 1, "message": "85", "line": 35, "column": 43, "nodeType": "82", "messageId": "83", "endLine": 35, "endColumn": 53}, "no-unused-vars", "'isAdmin' is assigned a value but never used.", "Identifier", "unusedVar", "'isStaff' is assigned a value but never used.", "'isCustomer' is assigned a value but never used."]