import React from 'react';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  Box,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  ContentCut as SalonIcon,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';

const Navbar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const menuItems = [
    { text: 'Dashboard', path: '/' },
    { text: 'Appointments', path: '/appointments' },
    { text: 'Customers', path: '/customers' },
    { text: 'Services', path: '/services' },
    { text: 'Staff', path: '/staff' },
    { text: 'Reports', path: '/reports' },
  ];

  const handleNavigation = (path) => {
    navigate(path);
  };

  return (
    <AppBar position="fixed" sx={{ zIndex: 1000 }}>
      <Toolbar sx={{ minHeight: '56px !important' }}>
        <SalonIcon sx={{ mr: 1 }} />
        <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
          Salon Management System
        </Typography>

        {!isMobile ? (
          <Box sx={{ display: 'flex', gap: 0.5 }}>
            {menuItems.map((item) => (
              <Button
                key={item.text}
                color="inherit"
                size="small"
                onClick={() => handleNavigation(item.path)}
                sx={{
                  backgroundColor: location.pathname === item.path ? 'rgba(255,255,255,0.15)' : 'transparent',
                  '&:hover': {
                    backgroundColor: 'rgba(255,255,255,0.2)',
                  },
                  textTransform: 'none',
                  fontSize: '0.875rem',
                  px: 2,
                }}
              >
                {item.text}
              </Button>
            ))}
          </Box>
        ) : (
          <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
            {menuItems.slice(0, 3).map((item) => (
              <Button
                key={item.text}
                color="inherit"
                size="small"
                onClick={() => handleNavigation(item.path)}
                sx={{
                  backgroundColor: location.pathname === item.path ? 'rgba(255,255,255,0.15)' : 'transparent',
                  '&:hover': {
                    backgroundColor: 'rgba(255,255,255,0.2)',
                  },
                  textTransform: 'none',
                  fontSize: '0.75rem',
                  px: 1,
                  minWidth: 'auto',
                }}
              >
                {item.text}
              </Button>
            ))}
          </Box>
        )}
      </Toolbar>
    </AppBar>
  );
};

export default Navbar;
